-- =====================================================
-- SAFARNI BOOKING SYSTEM SCHEMA FIX
-- This script fixes all database schema inconsistencies
-- =====================================================

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- 1. FIX BOOKINGS TABLE SCHEMA
-- =====================================================

-- Check current bookings table structure
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name='bookings' AND table_schema='public'
ORDER BY ordinal_position;

-- Add missing columns safely
ALTER TABLE public.bookings ADD COLUMN IF NOT EXISTS driver_id UUID;
ALTER TABLE public.bookings ADD COLUMN IF NOT EXISTS passenger_id UUID;
ALTER TABLE public.bookings ADD COLUMN IF NOT EXISTS booking_type TEXT DEFAULT 'manual';
ALTER TABLE public.bookings ADD COLUMN IF NOT EXISTS message TEXT;
ALTER TABLE public.bookings ADD COLUMN IF NOT EXISTS special_requests TEXT;
ALTER TABLE public.bookings ADD COLUMN IF NOT EXISTS passenger_details JSONB DEFAULT '{}';
ALTER TABLE public.bookings ADD COLUMN IF NOT EXISTS rejection_reason TEXT;
ALTER TABLE public.bookings ADD COLUMN IF NOT EXISTS total_price DECIMAL(10,2);
ALTER TABLE public.bookings ADD COLUMN IF NOT EXISTS is_paid BOOLEAN DEFAULT FALSE;
ALTER TABLE public.bookings ADD COLUMN IF NOT EXISTS payment_method TEXT;
ALTER TABLE public.bookings ADD COLUMN IF NOT EXISTS payment_reference TEXT;
ALTER TABLE public.bookings ADD COLUMN IF NOT EXISTS confirmed_at TIMESTAMPTZ;
ALTER TABLE public.bookings ADD COLUMN IF NOT EXISTS cancelled_at TIMESTAMPTZ;
ALTER TABLE public.bookings ADD COLUMN IF NOT EXISTS completed_at TIMESTAMPTZ;

-- Migrate traveler_id to passenger_id if needed
DO $$ 
BEGIN
    -- Check if traveler_id exists and passenger_id is empty
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'bookings' AND column_name = 'traveler_id') THEN
        
        -- Copy traveler_id to passenger_id for existing records
        UPDATE public.bookings 
        SET passenger_id = traveler_id 
        WHERE passenger_id IS NULL AND traveler_id IS NOT NULL;
        
        -- If all data is migrated, we can optionally drop traveler_id
        -- ALTER TABLE public.bookings DROP COLUMN IF EXISTS traveler_id;
    END IF;
END $$;

-- Update status constraint to include all expected values
ALTER TABLE public.bookings DROP CONSTRAINT IF EXISTS bookings_status_check;
ALTER TABLE public.bookings ADD CONSTRAINT bookings_status_check 
CHECK (status IN ('pending', 'accepted', 'rejected', 'confirmed', 'cancelled', 'completed'));

-- Add foreign key constraints if missing
ALTER TABLE public.bookings DROP CONSTRAINT IF EXISTS bookings_driver_id_fkey;
ALTER TABLE public.bookings DROP CONSTRAINT IF EXISTS bookings_passenger_id_fkey;
ALTER TABLE public.bookings DROP CONSTRAINT IF EXISTS bookings_trip_id_fkey;

ALTER TABLE public.bookings 
ADD CONSTRAINT bookings_driver_id_fkey 
FOREIGN KEY (driver_id) REFERENCES public.users(id) ON DELETE CASCADE;

ALTER TABLE public.bookings 
ADD CONSTRAINT bookings_passenger_id_fkey 
FOREIGN KEY (passenger_id) REFERENCES public.users(id) ON DELETE CASCADE;

ALTER TABLE public.bookings 
ADD CONSTRAINT bookings_trip_id_fkey 
FOREIGN KEY (trip_id) REFERENCES public.trips(id) ON DELETE CASCADE;

-- =====================================================
-- 2. CONVERSATIONS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS public.conversations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    booking_id UUID REFERENCES public.bookings(id) ON DELETE CASCADE,
    trip_id UUID REFERENCES public.trips(id) ON DELETE CASCADE,
    driver_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    passenger_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    last_message TEXT,
    last_message_at TIMESTAMPTZ,
    unread_count_driver INTEGER DEFAULT 0,
    unread_count_passenger INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- 3. MESSAGES TABLE (if not exists)
-- =====================================================

CREATE TABLE IF NOT EXISTS public.messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID REFERENCES public.conversations(id) ON DELETE CASCADE,
    trip_id UUID REFERENCES public.trips(id) ON DELETE CASCADE,
    booking_id UUID REFERENCES public.bookings(id) ON DELETE SET NULL,
    sender_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    receiver_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    message_type TEXT DEFAULT 'text' CHECK (message_type IN ('text', 'location', 'system', 'image')),
    location_data JSONB,
    is_read BOOLEAN DEFAULT FALSE,
    is_delivered BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- 4. INDEXES FOR PERFORMANCE
-- =====================================================

-- Bookings indexes
CREATE INDEX IF NOT EXISTS idx_bookings_driver_id ON public.bookings(driver_id);
CREATE INDEX IF NOT EXISTS idx_bookings_passenger_id ON public.bookings(passenger_id);
CREATE INDEX IF NOT EXISTS idx_bookings_trip_id ON public.bookings(trip_id);
CREATE INDEX IF NOT EXISTS idx_bookings_status ON public.bookings(status);
CREATE INDEX IF NOT EXISTS idx_bookings_driver_status ON public.bookings(driver_id, status);
CREATE INDEX IF NOT EXISTS idx_bookings_created_at ON public.bookings(created_at DESC);

-- Conversations indexes
CREATE INDEX IF NOT EXISTS idx_conversations_driver_id ON public.conversations(driver_id);
CREATE INDEX IF NOT EXISTS idx_conversations_passenger_id ON public.conversations(passenger_id);
CREATE INDEX IF NOT EXISTS idx_conversations_trip_id ON public.conversations(trip_id);
CREATE INDEX IF NOT EXISTS idx_conversations_booking_id ON public.conversations(booking_id);
CREATE INDEX IF NOT EXISTS idx_conversations_updated_at ON public.conversations(updated_at DESC);

-- Messages indexes
CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON public.messages(conversation_id);
CREATE INDEX IF NOT EXISTS idx_messages_trip_id ON public.messages(trip_id);
CREATE INDEX IF NOT EXISTS idx_messages_sender_id ON public.messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_messages_receiver_id ON public.messages(receiver_id);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON public.messages(created_at DESC);

-- =====================================================
-- 5. RLS POLICIES
-- =====================================================

-- Enable RLS
ALTER TABLE public.bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;

-- Bookings policies
DROP POLICY IF EXISTS "Users can view their own bookings" ON public.bookings;
CREATE POLICY "Users can view their own bookings" ON public.bookings
    FOR SELECT USING (passenger_id = auth.uid() OR driver_id = auth.uid());

DROP POLICY IF EXISTS "Passengers can create bookings" ON public.bookings;
CREATE POLICY "Passengers can create bookings" ON public.bookings
    FOR INSERT WITH CHECK (passenger_id = auth.uid());

DROP POLICY IF EXISTS "Users can update their bookings" ON public.bookings;
CREATE POLICY "Users can update their bookings" ON public.bookings
    FOR UPDATE USING (passenger_id = auth.uid() OR driver_id = auth.uid());

-- Conversations policies
DROP POLICY IF EXISTS "Users can view their conversations" ON public.conversations;
CREATE POLICY "Users can view their conversations" ON public.conversations
    FOR SELECT USING (driver_id = auth.uid() OR passenger_id = auth.uid());

DROP POLICY IF EXISTS "Users can create conversations" ON public.conversations;
CREATE POLICY "Users can create conversations" ON public.conversations
    FOR INSERT WITH CHECK (driver_id = auth.uid() OR passenger_id = auth.uid());

DROP POLICY IF EXISTS "Users can update their conversations" ON public.conversations;
CREATE POLICY "Users can update their conversations" ON public.conversations
    FOR UPDATE USING (driver_id = auth.uid() OR passenger_id = auth.uid());

-- Messages policies
DROP POLICY IF EXISTS "Users can view their messages" ON public.messages;
CREATE POLICY "Users can view their messages" ON public.messages
    FOR SELECT USING (sender_id = auth.uid() OR receiver_id = auth.uid());

DROP POLICY IF EXISTS "Users can send messages" ON public.messages;
CREATE POLICY "Users can send messages" ON public.messages
    FOR INSERT WITH CHECK (sender_id = auth.uid());

DROP POLICY IF EXISTS "Users can update their messages" ON public.messages;
CREATE POLICY "Users can update their messages" ON public.messages
    FOR UPDATE USING (sender_id = auth.uid() OR receiver_id = auth.uid());

-- =====================================================
-- 6. FUNCTIONS
-- =====================================================

-- Function to get driver bookings with passenger details
CREATE OR REPLACE FUNCTION get_driver_bookings_with_details(
    p_driver_id UUID,
    p_status TEXT DEFAULT NULL
) RETURNS TABLE (
    id UUID,
    trip_id UUID,
    passenger_id UUID,
    driver_id UUID,
    seats_booked INTEGER,
    total_price DECIMAL,
    booking_type TEXT,
    status TEXT,
    message TEXT,
    rejection_reason TEXT,
    special_requests TEXT,
    passenger_details JSONB,
    is_paid BOOLEAN,
    payment_method TEXT,
    payment_reference TEXT,
    confirmed_at TIMESTAMPTZ,
    cancelled_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    -- Trip details
    trip_title TEXT,
    trip_from_city TEXT,
    trip_to_city TEXT,
    trip_departure_date TIMESTAMPTZ,
    trip_price DECIMAL,
    -- Passenger details
    passenger_name TEXT,
    passenger_phone TEXT,
    passenger_email TEXT,
    passenger_profile_image_url TEXT,
    passenger_rating DECIMAL,
    passenger_total_ratings INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        b.id,
        b.trip_id,
        b.passenger_id,
        b.driver_id,
        b.seats_booked,
        b.total_price,
        b.booking_type,
        b.status,
        b.message,
        b.rejection_reason,
        b.special_requests,
        b.passenger_details,
        b.is_paid,
        b.payment_method,
        b.payment_reference,
        b.confirmed_at,
        b.cancelled_at,
        b.completed_at,
        b.created_at,
        b.updated_at,
        -- Trip details
        t.title as trip_title,
        t.from_city as trip_from_city,
        t.to_city as trip_to_city,
        t.departure_date as trip_departure_date,
        t.price as trip_price,
        -- Passenger details
        u.full_name as passenger_name,
        u.phone as passenger_phone,
        u.email as passenger_email,
        u.profile_image_url as passenger_profile_image_url,
        u.rating as passenger_rating,
        u.total_ratings as passenger_total_ratings
    FROM public.bookings b
    LEFT JOIN public.trips t ON b.trip_id = t.id
    LEFT JOIN public.users u ON b.passenger_id = u.id
    WHERE b.driver_id = p_driver_id
      AND (p_status IS NULL OR b.status = p_status)
    ORDER BY b.created_at DESC;
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER;

-- Function to create conversation when booking is accepted
CREATE OR REPLACE FUNCTION create_conversation_on_booking_acceptance()
RETURNS TRIGGER AS $$
BEGIN
    -- Only create conversation when status changes to 'accepted'
    IF NEW.status = 'accepted' AND OLD.status != 'accepted' THEN
        INSERT INTO public.conversations (
            booking_id,
            trip_id,
            driver_id,
            passenger_id,
            last_message,
            unread_count_passenger
        ) VALUES (
            NEW.id,
            NEW.trip_id,
            NEW.driver_id,
            NEW.passenger_id,
            'مرحباً! تم قبول طلب حجزك. يمكنك التواصل معي هنا لأي استفسارات.',
            1
        ) ON CONFLICT (booking_id) DO NOTHING; -- Prevent duplicate conversations
        
        -- Send initial system message
        INSERT INTO public.messages (
            trip_id,
            booking_id,
            sender_id,
            receiver_id,
            content,
            message_type
        ) VALUES (
            NEW.trip_id,
            NEW.id,
            NEW.driver_id,
            NEW.passenger_id,
            'مرحباً! تم قبول طلب حجزك. يمكنك التواصل معي هنا لأي استفسارات.',
            'system'
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic conversation creation
DROP TRIGGER IF EXISTS trigger_create_conversation_on_booking_acceptance ON public.bookings;
CREATE TRIGGER trigger_create_conversation_on_booking_acceptance
    AFTER UPDATE ON public.bookings
    FOR EACH ROW
    EXECUTE FUNCTION create_conversation_on_booking_acceptance();

-- Function to update conversation last message
CREATE OR REPLACE FUNCTION update_conversation_last_message()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE public.conversations
    SET 
        last_message = NEW.content,
        last_message_at = NEW.created_at,
        updated_at = NOW(),
        unread_count_driver = CASE 
            WHEN NEW.receiver_id = driver_id THEN unread_count_driver + 1 
            ELSE unread_count_driver 
        END,
        unread_count_passenger = CASE 
            WHEN NEW.receiver_id = passenger_id THEN unread_count_passenger + 1 
            ELSE unread_count_passenger 
        END
    WHERE trip_id = NEW.trip_id 
      AND (driver_id = NEW.sender_id OR driver_id = NEW.receiver_id)
      AND (passenger_id = NEW.sender_id OR passenger_id = NEW.receiver_id);
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updating conversation last message
DROP TRIGGER IF EXISTS trigger_update_conversation_last_message ON public.messages;
CREATE TRIGGER trigger_update_conversation_last_message
    AFTER INSERT ON public.messages
    FOR EACH ROW
    EXECUTE FUNCTION update_conversation_last_message();

-- =====================================================
-- 7. GRANT PERMISSIONS
-- =====================================================

GRANT EXECUTE ON FUNCTION get_driver_bookings_with_details(UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION create_conversation_on_booking_acceptance() TO authenticated;
GRANT EXECUTE ON FUNCTION update_conversation_last_message() TO authenticated;

-- =====================================================
-- 8. VERIFICATION QUERIES
-- =====================================================

-- Verify bookings table structure
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default 
FROM information_schema.columns 
WHERE table_name='bookings' AND table_schema='public'
ORDER BY ordinal_position;

-- Test the function
-- SELECT * FROM get_driver_bookings_with_details('your-driver-id'::uuid, 'pending');

-- Check existing data
SELECT 
    COUNT(*) as total_bookings,
    COUNT(driver_id) as bookings_with_driver,
    COUNT(passenger_id) as bookings_with_passenger,
    COUNT(DISTINCT status) as unique_statuses
FROM public.bookings;

COMMENT ON FUNCTION get_driver_bookings_with_details(UUID, TEXT) IS 'Returns driver bookings with full trip and passenger details including profile images';
COMMENT ON FUNCTION create_conversation_on_booking_acceptance() IS 'Automatically creates conversation when booking is accepted';
COMMENT ON FUNCTION update_conversation_last_message() IS 'Updates conversation last message when new message is sent';
