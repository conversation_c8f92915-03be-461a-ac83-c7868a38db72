// Test script to verify all booking system fixes
// Run this after applying the database schema fixes

import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class BookingSystemFixTest {
  static final _client = Supabase.instance.client;

  static Future<void> runCompleteTest() async {
    print('🧪 Starting Booking System Fix Test');
    print('=' * 50);

    try {
      // Test 1: Verify database schema
      await _testDatabaseSchema();
      
      // Test 2: Test new function
      await _testDriverBookingsFunction();
      
      // Test 3: Test booking acceptance
      await _testBookingAcceptance();
      
      // Test 4: Test booking rejection
      await _testBookingRejection();
      
      // Test 5: Test conversation creation
      await _testConversationCreation();
      
      print('\n✅ All tests completed successfully!');
      
    } catch (e) {
      print('❌ Test failed: $e');
    }
  }

  static Future<void> _testDatabaseSchema() async {
    print('\n📋 Testing Database Schema...');
    
    try {
      // Check if bookings table has required columns
      final result = await _client.rpc('get_table_columns', params: {
        'table_name': 'bookings'
      });
      
      final columns = result as List;
      final columnNames = columns.map((col) => col['column_name']).toList();
      
      final requiredColumns = [
        'id', 'trip_id', 'passenger_id', 'driver_id', 
        'seats_booked', 'total_price', 'status', 'booking_type',
        'message', 'rejection_reason', 'special_requests',
        'passenger_details', 'is_paid', 'confirmed_at',
        'created_at', 'updated_at'
      ];
      
      for (final col in requiredColumns) {
        if (columnNames.contains(col)) {
          print('  ✅ Column $col exists');
        } else {
          print('  ❌ Column $col missing');
        }
      }
      
      // Check conversations table
      final convResult = await _client.from('conversations').select('*').limit(1);
      print('  ✅ Conversations table accessible');
      
      // Check messages table
      final msgResult = await _client.from('messages').select('*').limit(1);
      print('  ✅ Messages table accessible');
      
    } catch (e) {
      print('  ❌ Schema test failed: $e');
    }
  }

  static Future<void> _testDriverBookingsFunction() async {
    print('\n🔧 Testing Driver Bookings Function...');
    
    try {
      // Test the new function with a dummy driver ID
      final testDriverId = '00000000-0000-0000-0000-000000000000';
      
      final result = await _client.rpc('get_driver_bookings_with_details', params: {
        'p_driver_id': testDriverId,
        'p_status': 'pending',
      });
      
      print('  ✅ Function get_driver_bookings_with_details works');
      print('  📊 Returned ${result.length} bookings');
      
      if (result.isNotEmpty) {
        final sample = result.first;
        print('  📋 Sample booking structure:');
        print('    - ID: ${sample['id']}');
        print('    - Trip title: ${sample['trip_title']}');
        print('    - Passenger name: ${sample['passenger_name']}');
        print('    - Passenger image: ${sample['passenger_profile_image_url']}');
      }
      
    } catch (e) {
      print('  ❌ Function test failed: $e');
    }
  }

  static Future<void> _testBookingAcceptance() async {
    print('\n✅ Testing Booking Acceptance...');
    
    try {
      // Create a test booking first
      final testBookingId = await _createTestBooking();
      
      if (testBookingId != null) {
        // Test acceptance
        await _client.from('bookings').update({
          'status': 'accepted',
        }).eq('id', testBookingId);
        
        print('  ✅ Booking status updated to accepted');
        
        // Check if conversation was created by trigger
        await Future.delayed(Duration(seconds: 2)); // Wait for trigger
        
        final conversations = await _client
            .from('conversations')
            .select('*')
            .eq('booking_id', testBookingId);
        
        if (conversations.isNotEmpty) {
          print('  ✅ Conversation created automatically by trigger');
          
          // Check if initial message was created
          final messages = await _client
              .from('messages')
              .select('*')
              .eq('booking_id', testBookingId);
          
          if (messages.isNotEmpty) {
            print('  ✅ Initial system message created');
          } else {
            print('  ⚠️ No initial message found');
          }
        } else {
          print('  ⚠️ No conversation created by trigger');
        }
        
        // Clean up
        await _client.from('bookings').delete().eq('id', testBookingId);
      }
      
    } catch (e) {
      print('  ❌ Acceptance test failed: $e');
    }
  }

  static Future<void> _testBookingRejection() async {
    print('\n❌ Testing Booking Rejection...');
    
    try {
      // Create a test booking first
      final testBookingId = await _createTestBooking();
      
      if (testBookingId != null) {
        // Test rejection with reason
        const rejectionReason = 'Test rejection reason';
        
        await _client.from('bookings').update({
          'status': 'rejected',
          'rejection_reason': rejectionReason,
        }).eq('id', testBookingId);
        
        print('  ✅ Booking status updated to rejected');
        
        // Verify rejection reason was saved
        final booking = await _client
            .from('bookings')
            .select('rejection_reason')
            .eq('id', testBookingId)
            .single();
        
        if (booking['rejection_reason'] == rejectionReason) {
          print('  ✅ Rejection reason saved correctly');
        } else {
          print('  ❌ Rejection reason not saved');
        }
        
        // Clean up
        await _client.from('bookings').delete().eq('id', testBookingId);
      }
      
    } catch (e) {
      print('  ❌ Rejection test failed: $e');
    }
  }

  static Future<void> _testConversationCreation() async {
    print('\n💬 Testing Conversation Creation...');
    
    try {
      // Test manual conversation creation
      final testConversationId = 'test-conversation-${DateTime.now().millisecondsSinceEpoch}';
      final testDriverId = '11111111-1111-1111-1111-111111111111';
      final testPassengerId = '22222222-2222-2222-2222-222222222222';
      final testTripId = '33333333-3333-3333-3333-333333333333';
      
      await _client.from('conversations').insert({
        'id': testConversationId,
        'driver_id': testDriverId,
        'passenger_id': testPassengerId,
        'trip_id': testTripId,
        'last_message': 'Test message',
        'unread_count_driver': 0,
        'unread_count_passenger': 1,
      });
      
      print('  ✅ Conversation created successfully');
      
      // Test message creation
      await _client.from('messages').insert({
        'conversation_id': testConversationId,
        'trip_id': testTripId,
        'sender_id': testDriverId,
        'receiver_id': testPassengerId,
        'content': 'Test message content',
        'message_type': 'text',
      });
      
      print('  ✅ Message created successfully');
      
      // Clean up
      await _client.from('messages').delete().eq('conversation_id', testConversationId);
      await _client.from('conversations').delete().eq('id', testConversationId);
      
    } catch (e) {
      print('  ❌ Conversation test failed: $e');
    }
  }

  static Future<String?> _createTestBooking() async {
    try {
      final testBookingId = 'test-booking-${DateTime.now().millisecondsSinceEpoch}';
      final testDriverId = '11111111-1111-1111-1111-111111111111';
      final testPassengerId = '22222222-2222-2222-2222-222222222222';
      final testTripId = '33333333-3333-3333-3333-333333333333';
      
      // Create test users if they don't exist
      await _client.from('users').upsert({
        'id': testDriverId,
        'email': '<EMAIL>',
        'full_name': 'Test Driver',
        'role': 'driver',
      });
      
      await _client.from('users').upsert({
        'id': testPassengerId,
        'email': '<EMAIL>',
        'full_name': 'Test Passenger',
        'role': 'passenger',
        'profile_image_url': 'https://example.com/avatar.jpg',
      });
      
      // Create test trip if it doesn't exist
      await _client.from('trips').upsert({
        'id': testTripId,
        'leader_id': testDriverId,
        'title': 'Test Trip',
        'from_city': 'Test From',
        'to_city': 'Test To',
        'departure_date': DateTime.now().add(Duration(days: 1)).toIso8601String(),
        'price': 100.0,
        'available_seats': 3,
        'total_seats': 4,
      });
      
      // Create test booking
      await _client.from('bookings').insert({
        'id': testBookingId,
        'trip_id': testTripId,
        'passenger_id': testPassengerId,
        'driver_id': testDriverId,
        'seats_booked': 1,
        'total_price': 100.0,
        'booking_type': 'manual',
        'status': 'pending',
        'message': 'Test booking message',
      });
      
      return testBookingId;
    } catch (e) {
      print('  ❌ Failed to create test booking: $e');
      return null;
    }
  }
}

// Run the test
void main() async {
  // Initialize Supabase (you'll need to add your credentials)
  await Supabase.initialize(
    url: 'YOUR_SUPABASE_URL',
    anonKey: 'YOUR_SUPABASE_ANON_KEY',
  );
  
  await BookingSystemFixTest.runCompleteTest();
}
