import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../constants/app_theme.dart';
import '../../constants/app_constants.dart';
import '../../providers/auth_provider.dart';
import '../../providers/trip_provider.dart';
import '../../models/trip_model.dart';
import '../../models/booking_model.dart';
import '../../services/storage_service.dart';
import '../../services/booking_service.dart';
import '../../widgets/trip_card.dart';
import '../../widgets/enhanced_driver_trip_card.dart';
import '../../utils/navigation_utils.dart';
import '../booking/booking_request_card.dart';
import 'create_trip_page.dart';
import 'trip_leader_profile_page.dart';

class TripLeaderDashboard extends StatefulWidget {
  const TripLeaderDashboard({super.key});

  @override
  State<TripLeaderDashboard> createState() => _TripLeaderDashboardState();
}

class _TripLeaderDashboardState extends State<TripLeaderDashboard>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int _pendingRequestsCount = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // Load data after the widget is built to avoid setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadDashboardData();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadDashboardData() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final tripProvider = Provider.of<TripProvider>(context, listen: false);

    if (authProvider.currentUser != null) {
      tripProvider.loadUserTrips(authProvider.currentUser!.id);
      _updatePendingRequestsCount();
    }
  }

  void _updatePendingRequestsCount() async {
    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final currentUser = authProvider.currentUser;

      if (currentUser != null) {
        final pendingBookings = await BookingService.getPendingBookings(currentUser.id);
        if (mounted) {
          setState(() {
            _pendingRequestsCount = pendingBookings.length;
          });
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading pending requests count: $e');
      }
      // Keep current count on error
    }
  }

  /// Create a test booking for debugging (only in debug mode)
  Future<void> _createTestBooking() async {
    if (!kDebugMode) return;

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;

    if (currentUser == null) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('لا يوجد مستخدم مسجل'),
            backgroundColor: AppColors.error,
          ),
        );
      }
      return;
    }

    try {
      if (kDebugMode) {
        print('🧪 Creating test booking for trip leader: ${currentUser.id}');
      }

      // Show loading
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('🧪 جاري إنشاء حجز تجريبي...'),
            backgroundColor: AppColors.info,
          ),
        );
      }

      // First, create a sample trip if needed
      final tripId = 'sample-trip-${DateTime.now().millisecondsSinceEpoch}';

      // Create a sample trip
      await Supabase.instance.client.from('trips').insert({
        'id': tripId,
        'leader_id': currentUser.id,
        'driver_id': currentUser.id,
        'title': 'رحلة تجريبية',
        'description': 'رحلة تجريبية لاختبار نظام الحجوزات',
        'from_city': 'الرباط',
        'to_city': 'الدار البيضاء',
        'departure_date': DateTime.now().add(const Duration(days: 1)).toIso8601String().split('T')[0],
        'departure_time': '08:00',
        'price': 50.0,
        'total_seats': 4,
        'available_seats': 4,
        'trip_type': 'mixed',
        'status': 'active',
        'allow_instant_booking': false,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      final result = await BookingService.createSampleBookingData(
        driverId: currentUser.id,
        tripId: tripId,
      );

      if (mounted) {
        if (result['success']) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['message'] ?? '✅ تم إنشاء حجز تجريبي بنجاح'),
              backgroundColor: AppColors.success,
            ),
          );
          // Refresh the pending requests count
          _updatePendingRequestsCount();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['error'] ?? '❌ فشل في إنشاء الحجز التجريبي'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error creating test booking: $e');
      }
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ حدث خطأ: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final authProvider = Provider.of<AuthProvider>(context);

    return Scaffold(
      backgroundColor: AppColors.background,
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 280,
              floating: false,
              pinned: true,
              backgroundColor: AppColors.primary,
              flexibleSpace: FlexibleSpaceBar(
                background: Container(
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Color(0xFF1E3A8A), // Deep Blue
                        Color(0xFF3B82F6), // Light Blue
                        Color(0xFF059669), // Emerald Green
                      ],
                    ),
                  ),
                  child: SafeArea(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Header Row with Profile and Actions
                          Row(
                            children: [
                              // Profile Picture
                              Container(
                                width: 60,
                                height: 60,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border:
                                      Border.all(color: Colors.white, width: 2),
                                  boxShadow: [
                                    BoxShadow(
                                      color:
                                          Colors.black.withValues(alpha: 0.2),
                                      blurRadius: 8,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: ClipOval(
                                  child: authProvider
                                              .currentUser?.profileImageUrl !=
                                          null
                                      ? CachedNetworkImage(
                                          imageUrl:
                                              StorageService.getProfileImageUrl(
                                                    authProvider
                                                        .currentUser!.id,
                                                    storedUrl: authProvider
                                                        .currentUser!
                                                        .profileImageUrl,
                                                  ) ??
                                                  '',
                                          width: 60,
                                          height: 60,
                                          fit: BoxFit.cover,
                                          placeholder: (context, url) =>
                                              Container(
                                            width: 60,
                                            height: 60,
                                            color: AppColors.primary
                                                .withValues(alpha: 0.3),
                                            child: const Center(
                                              child: CircularProgressIndicator(
                                                strokeWidth: 2,
                                                valueColor:
                                                    AlwaysStoppedAnimation<
                                                        Color>(Colors.white),
                                              ),
                                            ),
                                          ),
                                          errorWidget: (context, url, error) =>
                                              Container(
                                            width: 60,
                                            height: 60,
                                            color: AppColors.primary,
                                            child: const Icon(
                                              Icons.person,
                                              size: 30,
                                              color: Colors.white,
                                            ),
                                          ),
                                        )
                                      : Container(
                                          width: 60,
                                          height: 60,
                                          color: AppColors.primary,
                                          child: const Icon(
                                            Icons.person,
                                            size: 30,
                                            color: Colors.white,
                                          ),
                                        ),
                                ),
                              ),
                              const SizedBox(width: 16),

                              // User Info and Verified Badge
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Expanded(
                                          child: Text(
                                            authProvider
                                                    .currentUser?.fullName ??
                                                'قائد الرحلة',
                                            style: const TextStyle(
                                              color: Colors.white,
                                              fontSize: 18,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                        if (authProvider
                                                .currentUser?.isVerified ==
                                            true)
                                          Container(
                                            padding: const EdgeInsets.all(4),
                                            decoration: const BoxDecoration(
                                              color: Colors.blue,
                                              shape: BoxShape.circle,
                                            ),
                                            child: const Icon(
                                              Icons.check,
                                              color: Colors.white,
                                              size: 12,
                                            ),
                                          ),
                                      ],
                                    ),
                                    const SizedBox(height: 4),
                                    const Text(
                                      'قائد رحلات معتمد',
                                      style: TextStyle(
                                        color: Colors.white70,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                              // Notifications
                              IconButton(
                                icon: Stack(
                                  children: [
                                    const Icon(Icons.notifications,
                                        color: Colors.white),
                                    if (_pendingRequestsCount > 0)
                                      Positioned(
                                        right: 0,
                                        top: 0,
                                        child: Container(
                                          padding: const EdgeInsets.all(2),
                                          decoration: BoxDecoration(
                                            color: AppColors.error,
                                            borderRadius:
                                                BorderRadius.circular(10),
                                          ),
                                          constraints: const BoxConstraints(
                                            minWidth: 16,
                                            minHeight: 16,
                                          ),
                                          child: Text(
                                            '$_pendingRequestsCount',
                                            style: const TextStyle(
                                              color: Colors.white,
                                              fontSize: 10,
                                              fontWeight: FontWeight.bold,
                                            ),
                                            textAlign: TextAlign.center,
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                                onPressed: () {
                                  // TODO: Navigate to notifications
                                },
                              ),
                              // Debug button to create test booking (only in debug mode)
                              if (kDebugMode)
                                IconButton(
                                  icon: const Icon(
                                    Icons.add_circle_outline,
                                    color: Colors.white,
                                  ),
                                  onPressed: _createTestBooking,
                                  tooltip: 'إنشاء حجز تجريبي',
                                ),
                            ],
                          ),

                          const SizedBox(height: 20),

                          // Statistics Cards
                          Row(
                            children: [
                              // Balance Card
                              Expanded(
                                child: Container(
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withValues(alpha: 0.15),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color:
                                          Colors.white.withValues(alpha: 0.2),
                                    ),
                                  ),
                                  child: Column(
                                    children: [
                                      const Icon(
                                        Icons.account_balance_wallet,
                                        color: Colors.white,
                                        size: 24,
                                      ),
                                      const SizedBox(height: 8),
                                      Text(
                                        '${(authProvider.currentUser?.balance ?? 0.0).toStringAsFixed(2)} درهم',
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      const Text(
                                        'الرصيد',
                                        style: TextStyle(
                                          color: Colors.white70,
                                          fontSize: 12,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),

                              const SizedBox(width: 12),

                              // Total Trips Card
                              Expanded(
                                child: Container(
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withValues(alpha: 0.15),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color:
                                          Colors.white.withValues(alpha: 0.2),
                                    ),
                                  ),
                                  child: Column(
                                    children: [
                                      const Icon(
                                        Icons.directions_car,
                                        color: Colors.white,
                                        size: 24,
                                      ),
                                      const SizedBox(height: 8),
                                      Consumer<TripProvider>(
                                        builder:
                                            (context, tripProvider, child) {
                                          final totalTrips =
                                              tripProvider.userTrips.length;
                                          return Text(
                                            '$totalTrips',
                                            style: const TextStyle(
                                              color: Colors.white,
                                              fontSize: 16,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          );
                                        },
                                      ),
                                      const Text(
                                        'إجمالي الرحلات',
                                        style: TextStyle(
                                          color: Colors.white70,
                                          fontSize: 12,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),

                              const SizedBox(width: 12),

                              // Completed Trips Card
                              Expanded(
                                child: Container(
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withValues(alpha: 0.15),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color:
                                          Colors.white.withValues(alpha: 0.2),
                                    ),
                                  ),
                                  child: Column(
                                    children: [
                                      const Icon(
                                        Icons.check_circle,
                                        color: Colors.white,
                                        size: 24,
                                      ),
                                      const SizedBox(height: 8),
                                      Consumer<TripProvider>(
                                        builder:
                                            (context, tripProvider, child) {
                                          final completedTrips = tripProvider
                                              .userTrips
                                              .where((trip) =>
                                                  trip.status ==
                                                  AppConstants
                                                      .tripStatusCompleted)
                                              .length;
                                          return Text(
                                            '$completedTrips',
                                            style: const TextStyle(
                                              color: Colors.white,
                                              fontSize: 16,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          );
                                        },
                                      ),
                                      const Text(
                                        'رحلات مكتملة',
                                        style: TextStyle(
                                          color: Colors.white70,
                                          fontSize: 12,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              actions: [
                IconButton(
                  icon: const Icon(Icons.person, color: Colors.white),
                  onPressed: () {
                    NavigationUtils.pushWithTransition(
                      context,
                      const TripLeaderProfilePage(),
                      type: TransitionType.slide,
                    );
                  },
                ),
              ],
            ),
          ];
        },
        body: Column(
          children: [
            // Create Trip Button
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              child: Consumer<AuthProvider>(
                builder: (context, authProvider, child) {
                  final user = authProvider.currentUser;
                  final canCreateTrips = user?.canCreateTrips ?? false;

                  return Column(
                    children: [
                      // Modern Gradient Create Trip Button
                      Container(
                        width: double.infinity,
                        height: 56,
                        decoration: BoxDecoration(
                          gradient: canCreateTrips
                              ? const LinearGradient(
                                  begin: Alignment.centerLeft,
                                  end: Alignment.centerRight,
                                  colors: [
                                    Color(0xFF059669), // Emerald
                                    Color(0xFF10B981), // Light Emerald
                                    Color(0xFF34D399), // Very Light Emerald
                                  ],
                                )
                              : LinearGradient(
                                  colors: [
                                    AppColors.textSecondary,
                                    AppColors.textSecondary
                                        .withValues(alpha: 0.8),
                                  ],
                                ),
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: canCreateTrips
                              ? [
                                  BoxShadow(
                                    color: const Color(0xFF059669)
                                        .withValues(alpha: 0.3),
                                    blurRadius: 12,
                                    offset: const Offset(0, 4),
                                  ),
                                ]
                              : null,
                        ),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: canCreateTrips
                                ? () {
                                    NavigationUtils.pushWithTransition(
                                      context,
                                      const CreateTripPage(),
                                      type: TransitionType.slide,
                                    );
                                  }
                                : null,
                            borderRadius: BorderRadius.circular(16),
                            child: Container(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 24),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color:
                                          Colors.white.withValues(alpha: 0.2),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: const Icon(
                                      Icons.add,
                                      color: Colors.white,
                                      size: 20,
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  const Text(
                                    'إنشاء رحلة جديدة',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),

                      // Balance warning if insufficient
                      if (!canCreateTrips && user != null) ...[
                        const SizedBox(height: 8),
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.orange.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: Colors.orange.withValues(alpha: 0.3),
                            ),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.warning,
                                  color: Colors.orange, size: 20),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  'تحتاج إلى رصيد 5 درهم على الأقل لإنشاء رحلات جديدة. رصيدك الحالي: ${user.displayBalance}',
                                  style: TextStyle(
                                    color: Colors.orange.shade700,
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  );
                },
              ),
            ),

            const SizedBox(height: 8),

            // Tab Bar
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: TabBar(
                controller: _tabController,
                labelColor: AppColors.primary,
                unselectedLabelColor: AppColors.textSecondary,
                indicatorColor: AppColors.primary,
                tabs: [
                  Tab(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.directions_car),
                        const SizedBox(width: 8),
                        const Text('الرحلات النشطة'),
                      ],
                    ),
                  ),
                  Tab(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.check_circle),
                        const SizedBox(width: 8),
                        const Text('المكتملة'),
                      ],
                    ),
                  ),
                  Tab(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.pending),
                        const SizedBox(width: 8),
                        const Text('الطلبات'),
                        if (_pendingRequestsCount > 0) ...[
                          const SizedBox(width: 4),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Text(
                              '$_pendingRequestsCount',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Tab Views
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: const [
                  _ActiveTripsTab(),
                  _CompletedTripsTab(),
                  _PendingRequestsTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Active Trips Tab
class _ActiveTripsTab extends StatelessWidget {
  const _ActiveTripsTab();

  @override
  Widget build(BuildContext context) {
    return Consumer<TripProvider>(
      builder: (context, tripProvider, child) {
        // Show published trips (active trips for passengers)
        final activeTrips = tripProvider.userTrips
            .where((trip) =>
                trip.status == AppConstants.tripStatusPublished ||
                trip.status == AppConstants.tripStatusActive)
            .toList();

        if (tripProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (activeTrips.isEmpty) {
          return _buildEmptyState(
            context,
            icon: Icons.directions_car_outlined,
            title: 'لا توجد رحلات نشطة',
            subtitle: 'ابدأ بإنشاء رحلتك الأولى',
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: activeTrips.length,
          itemBuilder: (context, index) {
            return EnhancedDriverTripCard(trip: activeTrips[index]);
          },
        );
      },
    );
  }
}

// Completed Trips Tab
class _CompletedTripsTab extends StatelessWidget {
  const _CompletedTripsTab();

  @override
  Widget build(BuildContext context) {
    return Consumer<TripProvider>(
      builder: (context, tripProvider, child) {
        final completedTrips = tripProvider.userTrips
            .where((trip) => trip.status == AppConstants.tripStatusCompleted)
            .toList();

        if (tripProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (completedTrips.isEmpty) {
          return _buildEmptyState(
            context,
            icon: Icons.check_circle_outline,
            title: 'لا توجد رحلات مكتملة',
            subtitle: 'ستظهر رحلاتك المكتملة هنا',
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: completedTrips.length,
          itemBuilder: (context, index) {
            return EnhancedDriverTripCard(trip: completedTrips[index]);
          },
        );
      },
    );
  }
}

// Pending Requests Tab
class _PendingRequestsTab extends StatefulWidget {
  const _PendingRequestsTab();

  @override
  State<_PendingRequestsTab> createState() => _PendingRequestsTabState();
}

class _PendingRequestsTabState extends State<_PendingRequestsTab> {
  List<BookingModel> _pendingRequests = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPendingRequests();
  }

  Future<void> _loadPendingRequests() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;

    if (currentUser == null) {
      if (kDebugMode) {
        print('❌ Cannot load pending requests: No current user');
      }
      setState(() {
        _isLoading = false;
      });
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      if (kDebugMode) {
        print('🚗 Loading pending requests for driver: ${currentUser.id}');
      }

      final pendingBookings = await BookingService.fetchDriverBookings(
        driverId: currentUser.id,
        status: 'pending',
      );

      if (mounted) {
        setState(() {
          _pendingRequests = pendingBookings;
          _isLoading = false;
        });

        if (kDebugMode) {
          print('✅ Loaded ${pendingBookings.length} pending requests');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading pending requests: $e');
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحميل الطلبات: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _onBookingUpdated() {
    _loadPendingRequests();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_pendingRequests.isEmpty) {
      return _buildEmptyState(
        context,
        icon: Icons.pending_outlined,
        title: 'لا توجد طلبات معلقة',
        subtitle: 'ستظهر طلبات الحجز هنا',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _pendingRequests.length,
      itemBuilder: (context, index) {
        final booking = _pendingRequests[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: BookingRequestCard(
            booking: booking,
            onBookingUpdated: _onBookingUpdated,
          ),
        );
      },
    );
  }
}

Widget _buildEmptyState(
  BuildContext context, {
  required IconData icon,
  required String title,
  required String subtitle,
}) {
  final theme = Theme.of(context);

  return Center(
    child: Padding(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 80,
            color: AppColors.textTertiary,
          ),
          const SizedBox(height: 24),
          Text(
            title,
            style: theme.textTheme.titleLarge?.copyWith(
              color: AppColors.textSecondary,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: AppColors.textTertiary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    ),
  );
}
