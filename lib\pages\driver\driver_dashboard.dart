import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../constants/app_theme.dart';
import '../../models/booking_model.dart';
import '../../models/trip_model.dart';
import '../../models/conversation_model.dart';
import '../../providers/auth_provider.dart';
import '../../providers/trip_provider.dart';
import '../../services/booking_service.dart';
import '../../services/message_service.dart';
import '../../services/notification_service.dart';
import '../chat/chat_page.dart';
import '../../widgets/trip_card.dart';
import '../../widgets/enhanced_booking_request_card.dart';
import '../../utils/booking_test_helper.dart';
import '../../utils/booking_flow_test.dart';

class DriverDashboard extends StatefulWidget {
  const DriverDashboard({super.key});

  @override
  State<DriverDashboard> createState() => _DriverDashboardState();
}

class _DriverDashboardState extends State<DriverDashboard>
    with TickerProviderStateMixin {
  late TabController _tabController;

  List<BookingModel> _pendingBookings = [];
  List<BookingModel> _acceptedBookings = [];
  List<BookingModel> _bookings = []; // All bookings
  List<ConversationModel> _conversations = [];
  List<TripModel> _completedTrips = [];

  bool _isLoading = true;
  int _pendingRequestsCount = 0;
  int _unreadMessagesCount = 0;

  RealtimeChannel? _bookingChannel;
  RealtimeChannel? _messageChannel;
  RealtimeChannel? _notificationChannel;

  // Stream subscriptions for real-time updates
  StreamSubscription<List<BookingModel>>? _bookingSubscription;
  StreamSubscription<List<ConversationModel>>? _conversationSubscription;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);

    // Load data after the widget is built to avoid setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadDashboardData();
      _setupRealtimeSubscriptions();

      // Create sample data in debug mode if no bookings exist
      if (kDebugMode) {
        _createSampleDataIfNeeded();
      }
    });

    // Refresh pending requests when Requests tab becomes active
    _tabController.addListener(() {
      if (_tabController.index == 1 && !_tabController.indexIsChanging) {
        final authProvider = Provider.of<AuthProvider>(context, listen: false);
        final currentUser = authProvider.currentUser;
        if (currentUser != null) {
          if (kDebugMode) {
            print('🚗 Fetching pending bookings for driver: ${currentUser.id}');
          }
          _refreshRequests();
        }
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _cleanupSubscriptions();
    super.dispose();
  }

  Future<void> _loadDashboardData() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;

    if (currentUser == null) {
      if (kDebugMode) {
        print('❌ No current user found for dashboard');
      }
      return;
    }

    if (kDebugMode) {
      print('🔄 Loading dashboard data for driver: ${currentUser.id}');
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Load pending bookings with enhanced debugging
      if (kDebugMode) {
        print('📋 Loading pending bookings for driver: ${currentUser.id}');
        print('   Driver role: ${currentUser.role}');
        print('   Driver name: ${currentUser.fullName}');
      }

      final pendingBookings = await BookingService.getPendingBookings(currentUser.id);

      if (kDebugMode) {
        print('✅ Loaded ${pendingBookings.length} pending bookings');
        if (pendingBookings.isNotEmpty) {
          for (int i = 0; i < pendingBookings.length && i < 3; i++) {
            final booking = pendingBookings[i];
            print('   Booking ${i + 1}: ${booking.id}');
            print('     - Passenger: ${booking.passenger?.fullName ?? 'Unknown'}');
            print('     - Trip: ${booking.trip?.fromCity} → ${booking.trip?.toCity}');
            print('     - Status: ${booking.status}');
            print('     - Created: ${booking.createdAt}');
          }
        } else {
          print('   ⚠️ No pending bookings found for driver ${currentUser.id}');
        }
      }

      // Load all driver bookings with enhanced debugging
      if (kDebugMode) {
        print('📋 Loading all driver bookings...');
      }

      // Load all bookings initially, then set up real-time stream
      final allBookings = await BookingService.getDriverBookings(currentUser.id);
      final accepted = allBookings.where((b) => b.status == 'accepted').toList();

      if (kDebugMode) {
        print('✅ Loaded ${allBookings.length} total bookings, ${accepted.length} accepted');
        if (allBookings.isNotEmpty) {
          final statusCounts = <String, int>{};
          for (final booking in allBookings) {
            statusCounts[booking.status] = (statusCounts[booking.status] ?? 0) + 1;
          }
          print('   Status breakdown: $statusCounts');
        }
      }

      // Set up real-time booking subscription
      _setupBookingStream(currentUser.id);

      // Load conversations with enhanced debugging
      if (kDebugMode) {
        print('💬 Loading conversations...');
      }

      final conversations = await MessageService.getUserConversations(currentUser.id);

      if (kDebugMode) {
        print('✅ Loaded ${conversations.length} conversations');
        if (conversations.isNotEmpty) {
          for (int i = 0; i < conversations.length && i < 3; i++) {
            final conv = conversations[i];
            print('   Conversation ${i + 1}: ${conv.id}');
            print('     - Other user: ${conv.getOtherUserName(currentUser.id)}');
            print('     - Unread count: ${conv.getUnreadCount(currentUser.id)}');
            print('     - Last message: ${conv.lastMessage ?? 'No messages'}');
          }
        }
      }

      // Load completed trips
      if (mounted) {
        if (kDebugMode) {
          print('🚗 Loading completed trips...');
        }
        final tripProvider = Provider.of<TripProvider>(context, listen: false);
        await tripProvider.loadUserTrips(currentUser.id);
        final completedTrips = tripProvider.userTrips
            .where((trip) => trip.status == 'completed')
            .toList();
        if (kDebugMode) {
          print('✅ Loaded ${completedTrips.length} completed trips');
        }

        setState(() {
          _pendingBookings = pendingBookings;
          _acceptedBookings = accepted;
          _conversations = conversations;
          _completedTrips = completedTrips;
          _pendingRequestsCount = pendingBookings.length;
          _unreadMessagesCount = conversations
              .map((c) => c.getUnreadCount(currentUser.id))
              .fold(0, (sum, count) => sum + count);
          _isLoading = false;
        });

        if (kDebugMode) {
          print('🎉 Dashboard data loaded successfully');
          print('   - Pending requests: $_pendingRequestsCount');
          print('   - Accepted bookings: ${_acceptedBookings.length}');
          print('   - Conversations: ${_conversations.length}');
          print('   - Completed trips: ${_completedTrips.length}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading dashboard data: $e');
        print('Error type: ${e.runtimeType}');
        print('Stack trace: ${StackTrace.current}');
      }

      // Try fallback direct database query
      if (kDebugMode) {
        print('🔄 Attempting fallback direct database query...');
      }

      try {
        final fallbackBookings = await _loadBookingsFallback(currentUser.id);
        if (mounted) {
          setState(() {
            _pendingBookings = fallbackBookings.where((b) => b.status == 'pending').toList();
            _acceptedBookings = fallbackBookings.where((b) => b.status == 'accepted').toList();
            _pendingRequestsCount = _pendingBookings.length;
            _isLoading = false;
          });

          if (kDebugMode) {
            print('✅ Fallback query successful: ${_pendingBookings.length} pending, ${_acceptedBookings.length} accepted');
          }
          return; // Exit successfully
        }
      } catch (fallbackError) {
        if (kDebugMode) {
          print('❌ Fallback query also failed: $fallbackError');
        }
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحميل البيانات: $e'),
            backgroundColor: AppColors.error,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  void _setupRealtimeSubscriptions() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;

    if (currentUser == null) return;

    // Subscribe to booking updates
    _bookingChannel = Supabase.instance.client
        .channel('driver_bookings:${currentUser.id}')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: 'bookings',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'driver_id',
            value: currentUser.id,
          ),
          callback: (payload) {
            if (kDebugMode) {
              print('🔄 Booking update received: ${payload.eventType}');
            }
            _loadDashboardData(); // Refresh data when bookings change
          },
        )
        .subscribe();

    // Subscribe to conversation updates
    _messageChannel = Supabase.instance.client
        .channel('driver_conversations:${currentUser.id}')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: 'conversations',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'driver_id',
            value: currentUser.id,
          ),
          callback: (payload) {
            if (kDebugMode) {
              print('🔄 Conversation update received: ${payload.eventType}');
            }
            _loadDashboardData(); // Refresh data when conversations change
          },
        )
        .subscribe();

    // Subscribe to message updates for all conversations
    Supabase.instance.client
        .channel('driver_messages:${currentUser.id}')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: 'messages',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'receiver_id',
            value: currentUser.id,
          ),
          callback: (payload) {
            if (kDebugMode) {
              print('🔄 Message update received: ${payload.eventType}');
            }
            _loadDashboardData(); // Refresh data when new messages arrive
          },
        )
        .subscribe();

    // Subscribe to notifications
    _notificationChannel = NotificationService.subscribeToUserNotifications(
      currentUser.id,
      (notification) {
        if (notification.isBookingRequest) {
          _loadDashboardData(); // Refresh when new booking request arrives
        }
      },
    );
  }

  void _cleanupSubscriptions() {
    if (_bookingChannel != null) {
      Supabase.instance.client.removeChannel(_bookingChannel!);
    }
    if (_messageChannel != null) {
      MessageService.unsubscribeFromMessages(_messageChannel!);
    }
    if (_notificationChannel != null) {
      NotificationService.unsubscribeFromNotifications(_notificationChannel!);
    }
    _bookingSubscription?.cancel();
    _conversationSubscription?.cancel();
  }

  void _setupBookingStream(String driverId) {
    if (kDebugMode) {
      print('📡 Setting up real-time booking stream for driver: $driverId');
    }

    _bookingSubscription = BookingService.getDriverBookingsStream(driverId).listen(
      (bookings) {
        if (mounted) {
          setState(() {
            _bookings = bookings;
            _pendingBookings = bookings.where((b) => b.status == 'pending').toList();
            _acceptedBookings = bookings.where((b) => b.status == 'accepted').toList();
          });
          _updateCounts();

          if (kDebugMode) {
            print('📡 Real-time booking update: ${bookings.length} bookings');
            final statusCounts = <String, int>{};
            for (final booking in bookings) {
              statusCounts[booking.status] = (statusCounts[booking.status] ?? 0) + 1;
            }
            print('   Status breakdown: $statusCounts');
          }
        }
      },
      onError: (error) {
        if (kDebugMode) {
          print('❌ Error in booking stream: $error');
        }
      },
    );
  }

  void _updateCounts() {
    _pendingRequestsCount = _pendingBookings.length;
    final currentUser = context.read<AuthProvider>().currentUser;
    _unreadMessagesCount = _conversations
        .map((c) => c.getUnreadCount(currentUser?.id ?? ''))
        .fold(0, (sum, count) => sum + count);
  }

  // Debug method to create test booking
  Future<void> _createTestBooking() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;

    if (currentUser == null) return;

    try {
      // Create a test passenger if doesn't exist
      final testPassengerId = '22222222-2222-2222-2222-222222222222';

      // Check if test passenger exists
      final existingPassenger = await Supabase.instance.client
          .from('users')
          .select('id, full_name')
          .eq('id', testPassengerId)
          .maybeSingle();

      if (existingPassenger == null) {
        // Create test passenger
        await Supabase.instance.client
            .from('users')
            .insert({
              'id': testPassengerId,
              'full_name': 'فاطمة المسافرة',
              'role': 'traveler',
              'phone': '+212600000002',
              'city': 'الدار البيضاء',
              'email': '<EMAIL>',
            });
      }

      // Get the first trip of the current driver
      final tripProvider = Provider.of<TripProvider>(context, listen: false);
      final userTrips = tripProvider.userTrips;

      if (userTrips.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('لا توجد رحلات لإنشاء حجز تجريبي'),
              backgroundColor: AppColors.warning,
            ),
          );
        }
        return;
      }

      final trip = userTrips.first;

      // Create test booking
      final result = await BookingService.createBooking(
        tripId: trip.id,
        passengerId: testPassengerId,
        driverId: currentUser.id,
        seatsBooked: 1,
        totalPrice: trip.price,
        bookingType: 'manual',
        message: 'هذا حجز تجريبي لاختبار النظام',
        specialRequests: 'اختبار النظام',
        passengerName: 'فاطمة المسافرة',
        passengerPhone: '+212600000002',
      );

      if (mounted) {
        if (result['success']) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إنشاء حجز تجريبي بنجاح'),
              backgroundColor: AppColors.success,
            ),
          );
          _loadDashboardData(); // Refresh to show the new booking
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('فشل في إنشاء الحجز: ${result['error']}'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  // Create sample data if no bookings exist (debug mode only)
  Future<void> _createSampleDataIfNeeded() async {
    if (!kDebugMode) return;

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final currentUser = authProvider.currentUser;
      if (currentUser == null) return;

      // Check if driver has any bookings
      final existingBookings = await BookingService.getDriverBookingsByStatus(
        driverId: currentUser.id,
        status: 'pending',
      );

      if (existingBookings.isNotEmpty) {
        if (kDebugMode) {
          print('📊 Driver already has ${existingBookings.length} pending bookings');
        }
        return;
      }

      // Get driver's trips
      final tripProvider = Provider.of<TripProvider>(context, listen: false);
      final userTrips = tripProvider.userTrips;

      if (userTrips.isEmpty) {
        if (kDebugMode) {
          print('⚠️ No trips found for driver, cannot create sample bookings');
        }
        return;
      }

      // Create sample booking for the first trip
      final firstTrip = userTrips.first;
      final result = await BookingService.createSampleBookingData(
        driverId: currentUser.id,
        tripId: firstTrip.id,
      );

      if (result['success'] == true) {
        if (kDebugMode) {
          print('✅ Sample booking data created successfully');
        }
        // Refresh dashboard data to show the new booking
        _loadDashboardData();
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error creating sample data: $e');
      }
    }
  }

  // Fallback method to load bookings directly from database
  Future<List<BookingModel>> _loadBookingsFallback(String driverId) async {
    try {
      if (kDebugMode) {
        print('🔄 Executing fallback booking query for driver: $driverId');
      }

      final response = await Supabase.instance.client
          .from('bookings')
          .select('''
            *,
            trip:trips(*),
            passenger:users!passenger_id(*)
          ''')
          .eq('driver_id', driverId)
          .order('created_at', ascending: false);

      if (kDebugMode) {
        print('✅ Fallback query returned ${response.length} bookings');
      }

      return response.map<BookingModel>((json) => BookingModel.fromJson(json)).toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Fallback booking query failed: $e');
      }
      return [];
    }
  }

  // Refresh only the pending requests list using the booking service method
  Future<void> _refreshRequests() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;
    if (currentUser == null) return;

    try {
      final bookings = await BookingService.fetchDriverBookings(
        driverId: currentUser.id,
        status: 'pending',
      );
      if (!mounted) return;
      setState(() {
        _pendingBookings = bookings;
        _pendingRequestsCount = bookings.length;
      });
      if (kDebugMode) {
        print('📦 Pending bookings loaded: ${bookings.length}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error refreshing pending requests: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('لوحة تحكم السائق'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          if (kDebugMode)
            IconButton(
              onPressed: _createTestBooking,
              icon: const Icon(Icons.bug_report),
              tooltip: 'Create Test Booking',
            ),
          IconButton(
            onPressed: _loadDashboardData,
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: Column(
        children: [
          // Tab Bar
          Container(
            color: AppColors.primary,
            child: TabBar(
              controller: _tabController,
              indicatorColor: Colors.white,
              labelColor: Colors.white,
              unselectedLabelColor: Colors.white70,
              onTap: (index) {
                if (index == 1) {
                  final authProvider = Provider.of<AuthProvider>(context, listen: false);
                  final currentUser = authProvider.currentUser;
                  if (currentUser != null) {
                    if (kDebugMode) {
                      print('🚗 Fetching pending bookings for driver: ${currentUser.id}');
                    }
                    _refreshRequests();
                  }
                }
              },
              tabs: [
                const Tab(
                  icon: Icon(Icons.directions_car),
                  text: 'الرحلات النشطة',
                ),
                Tab(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.pending),
                      const SizedBox(width: 8),
                      const Text('الطلبات'),
                      if (_pendingRequestsCount > 0) ...[
                        const SizedBox(width: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: AppColors.error,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            '$_pendingRequestsCount',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                Tab(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.chat),
                      const SizedBox(width: 8),
                      const Text('المحادثات'),
                      if (_unreadMessagesCount > 0) ...[
                        const SizedBox(width: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: AppColors.secondary,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            '$_unreadMessagesCount',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                const Tab(
                  icon: Icon(Icons.check_circle),
                  text: 'المكتملة',
                ),
              ],
            ),
          ),

          // Tab Views
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : TabBarView(
                    controller: _tabController,
                    children: [
                      _ActiveTripsTab(bookings: _acceptedBookings),
                      _PendingRequestsTab(
                        bookings: _pendingBookings,
                        onAccept: _acceptBooking,
                        onReject: _rejectBooking,
                      ),
                      _ChatsTab(conversations: _conversations),
                      _CompletedTripsTab(trips: _completedTrips),
                    ],
                  ),
          ),
        ],
      ),
      // Debug FAB for creating sample data
      floatingActionButton: kDebugMode ? FloatingActionButton(
        onPressed: _createTestBooking,
        backgroundColor: AppColors.primary,
        child: const Icon(Icons.add, color: Colors.white),
        tooltip: 'تشغيل اختبار شامل للحجوزات',
      ) : null,
    );
  }

  void _acceptBooking(BookingModel booking) {
    () async {
      try {
        final result = await BookingService.acceptBooking(booking.id);

        if (result['success'] == true) {
          if (kDebugMode) {
            print('✅ Booking ${booking.id} accepted');
          }
          await _refreshRequests();
        } else {
          final error = result['error'] ?? 'حدث خطأ أثناء قبول الطلب';
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(error),
                backgroundColor: AppColors.error,
              ),
            );
          }
        }
      } catch (e) {
        if (kDebugMode) {
          print('❌ Error accepting booking ${booking.id}: $e');
        }
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('حدث خطأ أثناء قبول الطلب: $e'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    }();
  }

  void _rejectBooking(BookingModel booking) {
    () async {
      try {
        // Show rejection reason dialog
        final reason = await _showRejectionDialog();
        if (reason == null) return; // User cancelled

        final result = await BookingService.rejectBooking(
          booking.id,
          rejectionReason: reason.isEmpty ? null : reason,
        );

        if (result['success'] == true) {
          if (kDebugMode) {
            print('❌ Booking ${booking.id} rejected');
          }
          await _refreshRequests();
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(result['message'] ?? 'تم رفض الحجز'),
                backgroundColor: AppColors.warning,
              ),
            );
          }
        } else {
          final error = result['error'] ?? 'حدث خطأ أثناء رفض الطلب';
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(error),
                backgroundColor: AppColors.error,
              ),
            );
          }
        }
      } catch (e) {
        if (kDebugMode) {
          print('❌ Error rejecting booking ${booking.id}: $e');
        }
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('حدث خطأ أثناء رفض الطلب: $e'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    }();
  }

  Future<String?> _showRejectionDialog() async {
    final TextEditingController controller = TextEditingController();

    return showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('سبب الرفض'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('يمكنك إضافة سبب الرفض (اختياري):'),
            const SizedBox(height: 16),
            TextField(
              controller: controller,
              maxLines: 3,
              decoration: const InputDecoration(
                hintText: 'مثل: الرحلة ممتلئة، تغيير في الموعد...',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(controller.text),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('رفض الحجز'),
          ),
        ],
      ),
    );
  }

  // Manual sample booking creation for testing
  Future<void> _createSampleBookingManually() async {
    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final currentUser = authProvider.currentUser;
      if (currentUser == null) return;

      // Run comprehensive booking flow test
      await BookingFlowTest.runCompleteTest();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم إنشاء حجز تجريبي بنجاح')),
        );

        // Refresh data after a short delay to allow database to update
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            _loadDashboardData();
          }
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ: $e')),
        );
      }
    }
  }
}

// Active Trips Tab
class _ActiveTripsTab extends StatelessWidget {
  final List<BookingModel> bookings;

  const _ActiveTripsTab({required this.bookings});

  @override
  Widget build(BuildContext context) {
    if (bookings.isEmpty) {
      return _buildEmptyState(
        context,
        icon: Icons.directions_car_outlined,
        title: 'لا توجد رحلات نشطة',
        subtitle: 'ستظهر الرحلات المحجوزة هنا',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: bookings.length,
      itemBuilder: (context, index) {
        final booking = bookings[index];
        return Card(
          margin: const EdgeInsets.symmetric(vertical: 8),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    CircleAvatar(
                      backgroundImage: booking.passenger?.profileImageUrl != null
                          ? NetworkImage(booking.passenger!.profileImageUrl!)
                          : null,
                      child: booking.passenger?.profileImageUrl == null
                          ? const Icon(Icons.person)
                          : null,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            booking.passenger?.fullName ?? 'مسافر',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          Text(
                            '${booking.seatsBooked} مقعد - ${booking.totalPrice.toInt()} درهم',
                            style: TextStyle(
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => ChatPage(
                              tripId: booking.tripId,
                              otherUserId: booking.passengerId,
                              conversationId: null,
                            ),
                          ),
                        );
                      },
                      icon: const Icon(Icons.chat),
                      color: AppColors.primary,
                    ),
                  ],
                ),
                if (booking.message != null) ...[
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.surface,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      booking.message!,
                      style: TextStyle(
                        color: AppColors.textSecondary,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }
}

// Pending Requests Tab
class _PendingRequestsTab extends StatelessWidget {
  final List<BookingModel> bookings;
  final Function(BookingModel) onAccept;
  final Function(BookingModel) onReject;

  const _PendingRequestsTab({
    required this.bookings,
    required this.onAccept,
    required this.onReject,
  });

  @override
  Widget build(BuildContext context) {
    if (bookings.isEmpty) {
      return _buildEmptyState(
        context,
        icon: Icons.inbox_outlined,
        title: "لا توجد طلبات معلقة",
        subtitle: "ستظهر طلبات الحجز الجديدة هنا",
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(8),
      itemCount: bookings.length,
      itemBuilder: (context, index) {
        final booking = bookings[index];
        final passengerName = booking.passengerDetails?['name'] as String? ?? 'مسافر';
        final passengerPhoto = booking.passengerDetails?['photo_url'] as String?;

        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    CircleAvatar(
                      backgroundImage: passengerPhoto != null ? NetworkImage(passengerPhoto) : null,
                      child: passengerPhoto == null ? const Icon(Icons.person) : null,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(passengerName, style: const TextStyle(fontWeight: FontWeight.bold)),
                          Text(booking.trip?.fromCity != null ? '${booking.trip!.fromCity} -> ${booking.trip!.toCity}' : booking.tripId),
                        ],
                      ),
                    ),
                    Text(booking.timeAgo, style: Theme.of(context).textTheme.bodySmall),
                  ],
                ),
                const Divider(),
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('${booking.seatsBooked} مقاعد'),
                      Text('${booking.totalPrice.toStringAsFixed(2)} درهم'),
                      Text(booking.bookingType),
                    ],
                  ),
                ),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => onAccept(booking),
                        icon: const Icon(Icons.check),
                        label: const Text('قبول'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.success,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => onReject(booking),
                        icon: const Icon(Icons.close),
                        label: const Text('رفض'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: AppColors.error,
                          side: BorderSide(color: AppColors.error),
                        ),
                      ),
                    ),
                  ],
                )
              ],
            ),
          ),
        );
      },
    );
  }
}

// Chats Tab
class _ChatsTab extends StatelessWidget {
  final List<ConversationModel> conversations;

  const _ChatsTab({required this.conversations});

  @override
  Widget build(BuildContext context) {
    if (conversations.isEmpty) {
      return _buildEmptyState(
        context,
        icon: Icons.chat_outlined,
        title: 'لا توجد محادثات',
        subtitle: 'ستظهر محادثاتك مع المسافرين هنا بعد قبول الحجوزات',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: conversations.length,
      itemBuilder: (context, index) {
        return _ConversationCard(
          conversation: conversations[index],
          onTap: () => _openChat(context, conversations[index]),
        );
      },
    );
  }

  void _openChat(BuildContext context, ConversationModel conversation) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;

    if (currentUser == null || conversation.tripId == null) return;

    // Determine the other user ID (passenger for driver)
    final otherUserId = conversation.passengerId;

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChatPage(
          tripId: conversation.tripId!,
          otherUserId: otherUserId,
        ),
      ),
    );
  }
}

// Conversation Card Widget
class _ConversationCard extends StatelessWidget {
  final ConversationModel conversation;
  final VoidCallback onTap;

  const _ConversationCard({
    required this.conversation,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final currentUser = authProvider.currentUser;

    if (currentUser == null) return const SizedBox.shrink();

    // Get other user info
    final otherUserName = conversation.getOtherUserName(currentUser.id);
    final otherUserImage = conversation.getOtherUserImage(currentUser.id);
    final unreadCount = conversation.getUnreadCount(currentUser.id);

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        onTap: onTap,
        leading: Stack(
          children: [
            CircleAvatar(
              radius: 24,
              backgroundImage: otherUserImage != null
                  ? NetworkImage(otherUserImage)
                  : null,
              child: otherUserImage == null
                  ? const Icon(Icons.person, size: 24)
                  : null,
            ),
            // Show unread count badge if there are unread messages
            if (unreadCount > 0)
              Positioned(
                right: 0,
                top: 0,
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: const BoxDecoration(
                    color: AppColors.secondary,
                    shape: BoxShape.circle,
                  ),
                  child: Text(
                    '$unreadCount',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
          ],
        ),
        title: Text(
          otherUserName,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (conversation.tripFrom != null && conversation.tripTo != null)
              Text(
                '${conversation.tripFrom} ← ${conversation.tripTo}',
                style: TextStyle(
                  color: AppColors.textSecondary,
                  fontSize: 12,
                ),
              ),
            const SizedBox(height: 2),
            Text(
              conversation.lastMessage ?? 'لا توجد رسائل',
              style: TextStyle(
                color: AppColors.textTertiary,
                fontSize: 12,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
        trailing: conversation.lastMessageAt != null
            ? Text(
                _formatTime(conversation.lastMessageAt!),
                style: TextStyle(
                  color: AppColors.textTertiary,
                  fontSize: 11,
                ),
              )
            : null,
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }
}

// Completed Trips Tab
class _CompletedTripsTab extends StatelessWidget {
  final List<TripModel> trips;

  const _CompletedTripsTab({required this.trips});

  @override
  Widget build(BuildContext context) {
    if (trips.isEmpty) {
      return _buildEmptyState(
        context,
        icon: Icons.check_circle_outline,
        title: 'لا توجد رحلات مكتملة',
        subtitle: 'ستظهر رحلاتك المكتملة هنا',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: trips.length,
      itemBuilder: (context, index) {
        return TripCard(
          trip: trips[index],
          showBookButton: false,
          onTap: () {
            // Navigate to trip details
          },
        );
      },
    );
  }
}

Widget _buildEmptyState(
  BuildContext context, {
  required IconData icon,
  required String title,
  required String subtitle,
}) {
  return Center(
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          icon,
          size: 64,
          color: AppColors.textTertiary,
        ),
        const SizedBox(height: 16),
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: AppColors.textSecondary,
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 8),
        Text(
          subtitle,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textTertiary,
              ),
          textAlign: TextAlign.center,
        ),
      ],
    ),
  );
}
